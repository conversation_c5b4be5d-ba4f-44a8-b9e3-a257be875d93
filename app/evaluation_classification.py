import json
import os
import glob
import argparse
import pandas as pd
import csv
from datetime import datetime
from pathlib import Path

try:
    from openpyxl import Workbook
    from openpyxl.styles import PatternFill, Font, Alignment, Border, Side
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False
    print("Warning: openpyxl not available. Will create CSV report instead.")

def setup_logging():
    """Setup basic logging for evaluation."""
    import logging
    
    # Create logs directory if it doesn't exist
    logs_dir = Path("data/logs")
    logs_dir.mkdir(parents=True, exist_ok=True)
    
    # Create log filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = logs_dir / f"classification_evaluation_{timestamp}.log"
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()  # Also log to console
        ]
    )
    
    return logging.getLogger(__name__)

def load_json_file(file_path):
    """Load and parse JSON file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def extract_classification_results(data):
    """Extract classification results from the JSON data."""
    if not data:
        return []
    
    # Extract from analysis_result.documents
    if 'analysis_result' in data and 'documents' in data['analysis_result']:
        return data['analysis_result']['documents']
    
    return []

def extract_confidence_values(data):
    """Extract confidence values from bedrock response if available."""
    confidence_values = {}
    
    # For classification, confidence might be in the bedrock response
    # This is a placeholder - adjust based on actual confidence data structure
    if data and 'bedrock_response' in data:
        # Add confidence extraction logic here if available in the response
        pass
    
    return confidence_values

def get_pdf_path_from_source_file(source_file):
    """Extract PDF path from source_file field."""
    if source_file:
        # Convert to relative path from the workspace root
        if source_file.startswith('/home/<USER>/Documents/repositories/logistically/'):
            return source_file.replace('/home/<USER>/Documents/repositories/logistically/', '')
        return source_file
    return ""

def evaluate_single_file(output_file, true_file, file_name, logger):
    """Evaluate a single file pair and return comparison results."""
    logger.info(f"Evaluating: {output_file} vs {true_file}")
    
    # Load both files
    output_data = load_json_file(output_file)
    true_data = load_json_file(true_file)
    
    if not output_data or not true_data:
        logger.error(f"Failed to load data files")
        return None
    
    # Extract classification results
    output_docs = extract_classification_results(output_data)
    true_docs = extract_classification_results(true_data)
    
    # Get PDF path
    pdf_path = get_pdf_path_from_source_file(output_data.get('source_file', ''))
    
    # Create a mapping of page_no to doc_type for easier comparison
    output_mapping = {doc['page_no']: doc['doc_type'] for doc in output_docs}
    true_mapping = {doc['page_no']: doc['doc_type'] for doc in true_docs}
    
    # Get all unique page numbers from both datasets
    all_pages = set(output_mapping.keys()) | set(true_mapping.keys())
    
    results = []
    for page_no in sorted(all_pages):
        output_doc_type = output_mapping.get(page_no, "NOT_CLASSIFIED")
        true_doc_type = true_mapping.get(page_no, "NOT_FOUND")
        
        is_correct = output_doc_type == true_doc_type
        
        # Determine correctness status
        if true_doc_type == "NOT_FOUND":
            correct_status = "TRUE_DATA_NOT_FOUND"
        elif output_doc_type == "NOT_CLASSIFIED":
            correct_status = "NOT_CLASSIFIED"
        else:
            correct_status = "TRUE" if is_correct else "FALSE"
        
        results.append({
            'File Name': file_name,
            'Field Name': f"Page_{page_no}_doc_type",
            'True Data Value': true_doc_type,
            'Extracted Value': output_doc_type,
            'Confidence': "",  # Placeholder for confidence if available
            'Correct': correct_status,
            'PDF Link': pdf_path,
            'Moved PDF Path': pdf_path,  # Same as PDF Link for classification
            'Extracted JSON Path': output_file
        })
    
    return results

def create_csv_report(all_results, output_file, logger):
    """Create CSV report for classification evaluation."""
    logger.info(f"Creating CSV report: {output_file}")
    
    # Change extension to .csv
    csv_file = output_file.replace('.xlsx', '.csv')
    
    with open(csv_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        
        # Write header
        writer.writerow(['File Name', 'Field Name', 'True Data Value', 'Extracted Value', 
                        'Confidence', 'Correct', 'PDF Link', 'Moved PDF Path', 'Extracted JSON Path'])
        
        # Write all results
        for file_name, results in all_results.items():
            for result in results:
                writer.writerow([
                    result['File Name'],
                    result['Field Name'],
                    result['True Data Value'],
                    result['Extracted Value'],
                    result['Confidence'],
                    result['Correct'],
                    result['PDF Link'],
                    result['Moved PDF Path'],
                    result['Extracted JSON Path']
                ])
        
        # Write statistics summary
        writer.writerow([])
        writer.writerow(['=== STATISTICS SUMMARY ==='])
        
        # Calculate overall statistics
        all_field_results = []
        for results in all_results.values():
            all_field_results.extend(results)
        
        if all_field_results:
            total_count = len(all_field_results)
            correct_count = sum(1 for r in all_field_results if r['Correct'] == "TRUE")
            false_count = sum(1 for r in all_field_results if r['Correct'] == "FALSE")
            not_classified_count = sum(1 for r in all_field_results if r['Correct'] == "NOT_CLASSIFIED")
            true_data_not_found_count = sum(1 for r in all_field_results if r['Correct'] == "TRUE_DATA_NOT_FOUND")
            
            comparable_count = total_count - not_classified_count - true_data_not_found_count
            accuracy = round((correct_count / comparable_count * 100) if comparable_count > 0 else 0, 2)
            
            writer.writerow(['Total Classifications', total_count])
            writer.writerow(['Correct', correct_count])
            writer.writerow(['Incorrect', false_count])
            writer.writerow(['Not Classified', not_classified_count])
            writer.writerow(['True Data Not Found', true_data_not_found_count])
            writer.writerow(['Accuracy (%)', accuracy])
    
    logger.info(f"CSV report saved to: {csv_file}")

def create_excel_report(all_results, output_file, logger):
    """Create Excel report with formatting."""
    logger.info(f"Creating Excel report: {output_file}")
    
    # Create workbook and worksheet
    wb = Workbook()
    ws = wb.active
    ws.title = "Classification Evaluation"
    
    # Add headers
    headers = ['File Name', 'Field Name', 'True Data Value', 'Extracted Value', 
               'Confidence', 'Correct', 'PDF Link', 'Moved PDF Path', 'Extracted JSON Path']
    ws.append(headers)
    
    # Style headers
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
    header_font = Font(color="FFFFFF", bold=True)
    
    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=1, column=col)
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = Alignment(horizontal="center", vertical="center")
    
    # Add data rows
    row_idx = 2
    for file_name, results in all_results.items():
        for result in results:
            ws.cell(row=row_idx, column=1, value=result['File Name'])
            ws.cell(row=row_idx, column=2, value=result['Field Name'])
            ws.cell(row=row_idx, column=3, value=result['True Data Value'])
            ws.cell(row=row_idx, column=4, value=result['Extracted Value'])
            ws.cell(row=row_idx, column=5, value=result['Confidence'])
            ws.cell(row=row_idx, column=6, value=result['Correct'])
            
            # Add PDF hyperlink if path exists
            pdf_path = result['PDF Link']
            if pdf_path and os.path.exists(pdf_path):
                pdf_cell = ws.cell(row=row_idx, column=7)
                pdf_cell.hyperlink = f"file:///{os.path.abspath(pdf_path)}"
                pdf_cell.value = "Open PDF"
                pdf_cell.font = Font(color="0000FF", underline="single")
            else:
                ws.cell(row=row_idx, column=7, value="PDF not found")
            
            ws.cell(row=row_idx, column=8, value=result['Moved PDF Path'])
            ws.cell(row=row_idx, column=9, value=result['Extracted JSON Path'])
            
            # Apply formatting based on correctness
            if result['Correct'] == "TRUE":
                fill_color = PatternFill(start_color="C6EFCE", end_color="C6EFCE", fill_type="solid")  # Light green
            elif result['Correct'] == "FALSE":
                fill_color = PatternFill(start_color="FFC7CE", end_color="FFC7CE", fill_type="solid")  # Light red
            elif result['Correct'] == "NOT_CLASSIFIED":
                fill_color = PatternFill(start_color="FFFF99", end_color="FFFF99", fill_type="solid")  # Light yellow
            else:  # TRUE_DATA_NOT_FOUND
                fill_color = PatternFill(start_color="ADD8E6", end_color="ADD8E6", fill_type="solid")  # Light blue
            
            # Apply fill to the entire row
            for col in range(1, len(headers) + 1):
                cell = ws.cell(row=row_idx, column=col)
                cell.fill = fill_color
            
            row_idx += 1
    
    # Add statistics at the end
    stats_start_row = row_idx + 2
    ws.cell(row=stats_start_row, column=1, value="CLASSIFICATION STATISTICS")
    ws.cell(row=stats_start_row, column=1).font = Font(bold=True, size=14)
    stats_start_row += 1
    
    # Calculate overall statistics
    all_field_results = []
    for results in all_results.values():
        all_field_results.extend(results)
    
    if all_field_results:
        total_count = len(all_field_results)
        correct_count = sum(1 for r in all_field_results if r['Correct'] == "TRUE")
        false_count = sum(1 for r in all_field_results if r['Correct'] == "FALSE")
        not_classified_count = sum(1 for r in all_field_results if r['Correct'] == "NOT_CLASSIFIED")
        true_data_not_found_count = sum(1 for r in all_field_results if r['Correct'] == "TRUE_DATA_NOT_FOUND")
        
        comparable_count = total_count - not_classified_count - true_data_not_found_count
        accuracy = round((correct_count / comparable_count * 100) if comparable_count > 0 else 0, 2)
        
        stats = [
            ("Total Classifications", total_count),
            ("Correct", correct_count),
            ("Incorrect", false_count),
            ("Not Classified", not_classified_count),
            ("True Data Not Found", true_data_not_found_count),
            ("Accuracy (%)", accuracy)
        ]
        
        for stat_name, stat_value in stats:
            ws.cell(row=stats_start_row, column=1, value=stat_name)
            ws.cell(row=stats_start_row, column=2, value=stat_value)
            stats_start_row += 1
    
    # Auto-adjust column widths
    for column in ws.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws.column_dimensions[column_letter].width = adjusted_width
    
    # Save workbook
    wb.save(output_file)
    logger.info(f"Excel report saved to: {output_file}")

def find_file_pairs(output_dir, true_dir, logger):
    """Find matching pairs of output and true data files."""
    output_files = glob.glob(os.path.join(output_dir, "*_bedrock_response.json"))
    file_pairs = []
    
    for output_file in output_files:
        # Extract base filename
        output_filename = os.path.basename(output_file)
        
        # Look for corresponding true data file
        true_file = os.path.join(true_dir, output_filename)
        
        if os.path.exists(true_file):
            base_name = output_filename.replace("_bedrock_response.json", "")
            file_pairs.append((output_file, true_file, base_name))
            logger.info(f"Found pair: {base_name}")
        else:
            logger.warning(f"No true data file found for: {output_filename}")
    
    return file_pairs

def main():
    """Main evaluation function."""
    parser = argparse.ArgumentParser(description='Evaluate classification results against true data')
    parser.add_argument('--output-dir', '-o', default='data/output_data/classification',
                       help='Directory containing output classification files')
    parser.add_argument('--true-dir', '-t', default='data/true_data/classification',
                       help='Directory containing true data classification files')
    parser.add_argument('--output-file', '-f', default='data/evaluation/classification_evaluation_report.xlsx',
                       help='Output Excel file path')
    
    args = parser.parse_args()
    
    # Setup logging
    logger = setup_logging()
    logger.info("Starting classification evaluation process")
    
    # Ensure output directory exists
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Find file pairs
    file_pairs = find_file_pairs(args.output_dir, args.true_dir, logger)
    
    if not file_pairs:
        logger.error("No matching file pairs found!")
        return
    
    # Evaluate all files
    all_results = {}
    for output_file, true_file, base_name in file_pairs:
        results = evaluate_single_file(output_file, true_file, base_name, logger)
        if results:
            all_results[base_name] = results
    
    if not all_results:
        logger.error("No successful evaluations!")
        return
    
    # Create report
    if OPENPYXL_AVAILABLE:
        create_excel_report(all_results, args.output_file, logger)
    else:
        create_csv_report(all_results, args.output_file, logger)
    
    # Print summary
    logger.info("\n=== CLASSIFICATION EVALUATION SUMMARY ===")
    
    all_field_results = []
    for results in all_results.values():
        all_field_results.extend(results)
    
    if all_field_results:
        total = len(all_field_results)
        correct = sum(1 for r in all_field_results if r['Correct'] == "TRUE")
        incorrect = sum(1 for r in all_field_results if r['Correct'] == "FALSE")
        not_classified = sum(1 for r in all_field_results if r['Correct'] == "NOT_CLASSIFIED")
        true_data_not_found = sum(1 for r in all_field_results if r['Correct'] == "TRUE_DATA_NOT_FOUND")
        
        comparable = total - not_classified - true_data_not_found
        accuracy = (correct / comparable * 100) if comparable > 0 else 0
        
        logger.info(f"Total Classifications: {total}")
        logger.info(f"Correct: {correct}")
        logger.info(f"Incorrect: {incorrect}")
        logger.info(f"Not Classified: {not_classified}")
        logger.info(f"True Data Not Found: {true_data_not_found}")
        logger.info(f"Accuracy: {accuracy:.1f}%")

def run_classification_evaluation(output_dir="data/output_data/classification",
                                 true_dir="data/true_data/classification",
                                 output_file=None):
    """
    Run classification evaluation with direct function call (for VS Code terminal usage).

    Args:
        output_dir (str): Directory containing output classification files
        true_dir (str): Directory containing true data classification files
        output_file (str): Output Excel file path (optional, auto-generated if None)

    Returns:
        dict: Evaluation results summary
    """
    from datetime import datetime

    # Setup logging
    logger = setup_logging()
    logger.info("Starting classification evaluation process")

    # Auto-generate output file if not provided
    if output_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"data/evaluation/classification_evaluation_{timestamp}.xlsx"

    # Ensure output directory exists
    os.makedirs(os.path.dirname(output_file), exist_ok=True)

    logger.info(f"Output directory: {output_dir}")
    logger.info(f"True data directory: {true_dir}")

    # Find file pairs
    file_pairs = find_file_pairs(output_dir, true_dir, logger)

    if not file_pairs:
        logger.error("No matching file pairs found!")
        return None

    # Evaluate all files
    all_results = {}
    logger.info(f"Found {len(file_pairs)} file pairs to evaluate")

    for i, (output_file_path, true_file_path, base_name) in enumerate(file_pairs, 1):
        logger.info(f"Processing file {i}/{len(file_pairs)}: {base_name}")
        results = evaluate_single_file(output_file_path, true_file_path, base_name, logger)
        if results:
            all_results[base_name] = results
            logger.info(f"  ✅ Evaluation successful for {base_name}")
        else:
            logger.warning(f"  ❌ Evaluation failed for {base_name}")

    logger.info(f"Successfully evaluated {len(all_results)} out of {len(file_pairs)} files")

    if not all_results:
        logger.error("No successful evaluations!")
        return None

    # Create report
    if OPENPYXL_AVAILABLE:
        create_excel_report(all_results, output_file, logger)
    else:
        create_csv_report(all_results, output_file, logger)

    # Calculate summary statistics
    all_field_results = []
    for results in all_results.values():
        all_field_results.extend(results)

    summary = {}
    if all_field_results:
        total = len(all_field_results)
        correct = sum(1 for r in all_field_results if r['Correct'] == "TRUE")
        incorrect = sum(1 for r in all_field_results if r['Correct'] == "FALSE")
        not_classified = sum(1 for r in all_field_results if r['Correct'] == "NOT_CLASSIFIED")
        true_data_not_found = sum(1 for r in all_field_results if r['Correct'] == "TRUE_DATA_NOT_FOUND")

        comparable = total - not_classified - true_data_not_found
        accuracy = (correct / comparable * 100) if comparable > 0 else 0

        summary = {
            'total_classifications': total,
            'correct': correct,
            'incorrect': incorrect,
            'not_classified': not_classified,
            'true_data_not_found': true_data_not_found,
            'comparable': comparable,
            'accuracy': accuracy
        }

        logger.info("\n=== CLASSIFICATION EVALUATION SUMMARY ===")
        logger.info(f"Total Classifications: {total}")
        logger.info(f"Correct: {correct}")
        logger.info(f"Incorrect: {incorrect}")
        logger.info(f"Not Classified: {not_classified}")
        logger.info(f"True Data Not Found: {true_data_not_found}")
        logger.info(f"Accuracy: {accuracy:.1f}%")

    logger.info(f"\n📊 Report saved to: {output_file}")
    return {
        'output_file': output_file,
        'summary': summary,
        'total_files': len(all_results)
    }

if __name__ == "__main__":
    main()
